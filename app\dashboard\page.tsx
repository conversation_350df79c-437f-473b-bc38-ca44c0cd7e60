"use client"

import { useState, useEffect, useRef } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { Activity, DollarSign, Per<PERSON>, AlertTriangle } from "lucide-react"
import { useRouter } from 'next/navigation'
import { ResponsiveSankey } from '@nivo/sankey'
import { secureFetch, API_CONFIG } from '@/app/config/api'

import { Header } from "@/components/header"
import { OverviewCard } from "@/components/overview-card"
import { AccountDetailsCard } from "@/components/account-details-card"
import { Card } from "@/components/ui/card"
import { 
  Table, TableBody, TableCell, TableHead, TableHeader, TableRow 
} from "@/components/ui/table"
import TradingChallenge from '../component/trading-challenge'

interface TradeDetail {
  openTime: string
  closeTime: string
  symbol: string
  action: string
  sizing: {
    type: string
    value: string
  }
  openPrice: number
  closePrice: number
  profit: number
}

interface AccountDetail {
  balance: number
  equity: number
  totalTrades: number
  drawdown: number
  profit: number
}

interface FormattedTrade {
  id: string
  symbol: string
  type: string
  openPrice: number
  profit: number
  date: string
  volume: number
}

interface PerformanceData {
  period: string
  accountBalance: number
  portfolioEquity: number
}

interface DrawdownData {
  maxDrawdown: number
  currentDrawdown: number
}

interface Order {
  id: string;
  symbol: string;
  action: string;
  sizing: {
    type: string;
    value: string;
  };
  openPrice: number;
  closePrice: number;
  profit: number;
}

export default function DashboardPage() {
  const router = useRouter();
  const [accountDetails, setAccountDetails] = useState<AccountDetail | null>(null)
  const [tradeHistory, setTradeHistory] = useState<FormattedTrade[]>([])
  const [performanceData, setPerformanceData] = useState<PerformanceData[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [drawdownData, setDrawdownData] = useState<DrawdownData>({
    maxDrawdown: 0,
    currentDrawdown: 0,
  })
  const [hasOrders, setHasOrders] = useState<boolean | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [sessionId, setSessionId] = useState('');
  const [terminalId, setTerminalId] = useState('');
  const [mounted, setMounted] = useState(false);
  const isMounted = useRef(true);

  // Check for authentication first
  useEffect(() => {
    const token = localStorage.getItem('access_token');
    if (!token) {
      router.push('/login');
    }
  }, [router]);

  useEffect(() => {
    setMounted(true);
    return () => {
      isMounted.current = false;
    };
  }, []);

  // Check for orders
  useEffect(() => {
    const checkOrders = async () => {
      if (!mounted) return;

      try {
        setIsLoading(true);
        const token = localStorage.getItem('access_token');
        if (!token) {
          setError('No access token found');
          setHasOrders(false);
          return;
        }

        console.log('Fetching orders from API...');
        const response = await secureFetch('/order/order_ids');

        if (!response.ok) {
          console.error('Order fetch failed with status:', response.status);
          throw new Error(`Failed to fetch orders (Status: ${response.status})`);
        }

        const responseData = await response.json();
        console.log('Orders API response:', responseData);

        if (!isMounted.current) return;

        // Handle both wrapped and direct array responses
        let orders = [];
        if (responseData && responseData.data && Array.isArray(responseData.data)) {
          orders = responseData.data;
        } else if (Array.isArray(responseData)) {
          orders = responseData;
        } else {
          console.warn('Unexpected response format:', responseData);
          setHasOrders(false);
          return;
        }

        console.log('Processed orders:', orders);
        setHasOrders(orders.length > 0);

        if (orders.length > 0) {
          const firstOrder = orders[0];
          const orderId = firstOrder.id || firstOrder.order_id;
          console.log('Setting selected account ID:', orderId);
          if (orderId) {
            localStorage.setItem('selectedAccountId', orderId.toString());
          }
        } else {
          console.log('No orders found for user');
        }
      } catch (err) {
        console.error('Error checking orders:', err);
        if (err instanceof Error) {
          setError(err.message);
        } else {
          setError('An error occurred while checking orders');
        }
        setHasOrders(false);
      } finally {
        if (isMounted.current) {
          setIsLoading(false);
        }
      }
    };

    checkOrders();
  }, [mounted]);

  // Initialize session and terminal IDs
  useEffect(() => {
    if (mounted) {
      const storedSessionId = localStorage.getItem('session_id');
      const storedTerminalId = localStorage.getItem('terminal_id');
      
      if (storedSessionId) setSessionId(storedSessionId);
      if (storedTerminalId) setTerminalId(storedTerminalId);
    }
  }, [mounted]);

  // Fetch data when we have orders
  useEffect(() => {
    if (hasOrders && mounted) {
      fetchData();
    }
  }, [hasOrders, mounted]);

  const LoadingSpinner = () => (
    <div className="min-h-screen flex items-center justify-center">
      <motion.div
        animate={{ 
          scale: [1, 1.2, 1],
          rotate: [0, 360]
        }}
        transition={{
          duration: 2,
          repeat: Infinity,
          ease: "easeInOut"
        }}
        className="w-16 h-16 border-4 border-orange-500 border-t-transparent rounded-full"
      />
    </div>
  );

  const fetchData = async () => {
    try {
      if (!isMounted.current) return;

      const formData = new FormData()
      formData.append('session', sessionId)
      formData.append('account_id', terminalId)

      console.log('Fetching MyFXBook data with:', { sessionId, terminalId });

      const response = await secureFetch(API_CONFIG.endpoints.myfxbookFetch, {
        method: 'POST',
        body: formData
      })

      if (!isMounted.current) return;

      if (!response.ok) {
        console.warn('MyFXBook fetch failed, setting default values');
        setAccountDetails({
          balance: 0,
          equity: 0,
          totalTrades: 0,
          drawdown: 0,
          profit: 0,
        });
        setTradeHistory([]);
        setDrawdownData({
          maxDrawdown: 0,
          currentDrawdown: 0,
        });
        setIsLoading(false);
        return;
      }

      const responseData = await response.json()
      const data = responseData.data || responseData;

      console.log('MyFXBook data received:', data);

      if (!isMounted.current) return;

      setAccountDetails({
        balance: data.account_info?.balance || 0,
        equity: data.account_info?.equity || 0,
        totalTrades: data.history?.length || 0,
        drawdown: data.account_info?.drawdown || 0,
        profit: data.account_info?.profit || 0,
      });

      const formattedTrades = (data.history || []).map((trade: TradeDetail) => ({
        id: trade.openTime || '',
        symbol: trade.symbol || '',
        type: trade.action || '',
        openPrice: trade.openPrice || 0,
        profit: trade.profit || 0,
        date: trade.openTime || '',
        volume: trade.sizing?.value || 0
      }));

      if (!isMounted.current) return;

      setTradeHistory(formattedTrades);
      setDrawdownData({
        maxDrawdown: data.account_info?.drawdown || 0,
        currentDrawdown: data.account_info?.drawdown || 0,
      });
      setIsLoading(false);
    } catch (error) {
      console.error('Error fetching MyFXBook data:', error);
      if (!isMounted.current) return;

      setAccountDetails({
        balance: 0,
        equity: 0,
        totalTrades: 0,
        drawdown: 0,
        profit: 0,
      });
      setTradeHistory([]);
      setDrawdownData({
        maxDrawdown: 0,
        currentDrawdown: 0,
      });
      setIsLoading(false);
    }
  }

  useEffect(() => {
    let interval: NodeJS.Timeout;
    
    const handleStorageChange = () => {
      if (!mounted || !isMounted.current) return;
      
      const newSessionId = localStorage.getItem('session_id') || '';
      const newTerminalId = localStorage.getItem('terminal_id') || '';
      
      if (newSessionId !== sessionId) {
        setSessionId(newSessionId);
      }
      if (newTerminalId !== terminalId) {
        setTerminalId(newTerminalId);
      }
    };

    if (mounted) {
      window.addEventListener('storage', handleStorageChange);
      interval = setInterval(handleStorageChange, 1000);
    }

    return () => {
      window.removeEventListener('storage', handleStorageChange);
      clearInterval(interval);
    };
  }, [sessionId, terminalId, mounted]);

  if (!mounted) {
    return null;
  }

  if (isLoading) {
    return <LoadingSpinner />;
  }

  // Only show trading challenge if we explicitly know there are no orders
  if (hasOrders === false) {
    return <TradingChallenge isDashboard={true} />;
  }

  const formatBalance = (balance?: number) => {
    return balance ? `$${balance.toLocaleString()}` : '$0'
  }

  const calculateDrawdown = (drawdown?: number) => {
    return drawdown ? `${drawdown.toFixed(2)}%` : '0%'
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-red-400 text-center">
          <p className="text-xl font-semibold">Error loading dashboard</p>
          <p className="mt-2">{error}</p>
        </div>
      </div>
    );
  }

  return (
    <main className="container mx-auto py-8 px-4">
      <Header />
      {/* <div>
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, type: "spring" }}
          className="grid grid-cols-1 lg:grid-cols-4 gap-4"
        >
          <OverviewCard
            title="Profit"
            value={formatBalance(accountDetails?.profit)}
            change=""
            icon={<Percent className="h-5 w-5 text-green-400" />}
          />
          <OverviewCard
            title="Account Balance"
            value={formatBalance(accountDetails?.balance)}
            change=""
            icon={<DollarSign className="h-5 w-5 text-blue-400" />}
          />
          <OverviewCard
            title="Total Trades" 
            value={accountDetails?.totalTrades?.toString() || "0"}
            change=""
            icon={<Activity className="h-5 w-5 text-purple-400" />}
          />
          <OverviewCard
            title="Daily Drawdown"
            value={calculateDrawdown(drawdownData.currentDrawdown)}
            change=""
            icon={<AlertTriangle className="h-5 w-5 text-red-400" />}
          />
        </motion.div> */}

        <AccountDetailsCard
          orderId={mounted ? localStorage.getItem('selectedAccountId') || "" : ""}
        />

        {/* <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4, duration: 0.8 }}
        >
          <Card className="p-6 bg-[#0d2339]/80 border-gray-800/50 backdrop-blur-sm">
            <h2 className="text-xl font-bold bg-gradient-to-r from-orange-400 to-orange-600 bg-clip-text text-transparent mb-6">
              Recent Trades
            </h2>
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="text-gray-400">Symbol</TableHead>
                    <TableHead className="text-gray-400">Type</TableHead>
                    <TableHead className="text-gray-400">Volume</TableHead>
                    <TableHead className="text-gray-400">Entry Price</TableHead>
                    <TableHead className="text-gray-400">P/L</TableHead>
                    <TableHead className="text-gray-400">Date</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {tradeHistory.slice(0, 5).map((trade, index) => (
                    <TableRow key={trade.id} className="hover:bg-white/5">
                      <TableCell className="font-medium">{trade.symbol}</TableCell>
                      <TableCell className={trade.type === 'Buy' ? 'text-green-400' : 'text-red-400'}>
                        {trade.type}
                      </TableCell>
                      <TableCell>{trade.volume}</TableCell>
                      <TableCell>{trade.openPrice}</TableCell>
                      <TableCell className={trade.profit >= 0 ? 'text-green-400' : 'text-red-400'}>
                        ${trade.profit.toLocaleString()}
                      </TableCell>
                      <TableCell>{trade.date}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </Card>
        </motion.div>
      </div> */}
    </main>
  )
}

function StatItem({ label, value, icon }: { label: string, value: string, icon: React.ReactNode }) {
  return (
    <div className="flex items-center justify-between p-4 bg-white/5 rounded-lg">
      <div className="flex items-center gap-3">
        <div className="p-2 rounded-lg bg-white/5">
          {icon}
        </div>
        <span className="text-gray-400">{label}</span>
      </div>
      <span className="font-semibold text-white">{value}</span>
    </div>
  )
}