"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { ArrowRight } from "lucide-react"
import Image from "next/image"
import Link from "next/link"
import { useEffect, useState } from "react"

const HeroSection = () => {
  const [scrollY, setScrollY] = useState(0)
  const [isMounted, setIsMounted] = useState(false)
  const [isMobile, setIsMobile] = useState(false)

  useEffect(() => {
    setIsMounted(true)

    // Check if mobile on mount and when window resizes
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768)
    }

    checkMobile()
    window.addEventListener("resize", checkMobile)

    const handleScroll = () => {
      setScrollY(window.scrollY)
    }

    window.addEventListener("scroll", handleScroll, { passive: true })
    return () => {
      window.removeEventListener("scroll", handleScroll)
      window.removeEventListener("resize", checkMobile)
    }
  }, [])

  return (
    <div className="relative min-h-screen overflow-hidden bg-gradient-to-b from-[#0A1426] via-[#0F1A2E] to-[#121E36]">
      {/* Background elements - only visible on desktop */}
      <div className="absolute inset-0 overflow-hidden opacity-10 hidden md:block">
        {/* Candlestick pattern background */}
        <div className="absolute top-0 left-0 w-full h-full">
          <svg width="100%" height="100%" viewBox="0 0 1000 1000" preserveAspectRatio="none">
            <g stroke="currentColor" strokeWidth="1" fill="none">
              {Array.from({ length: 10 }).map((_, i) => (
                <g key={i} className={i % 2 === 0 ? "text-green-500" : "text-red-500"}>
                  <line x1={100 + i * 100} y1={200 + Math.random() * 600} x2={100 + i * 100} y2={800} />
                  <rect
                    x={95 + i * 100}
                    y={300 + Math.random() * 400}
                    width="10"
                    height={100 + Math.random() * 200}
                    fill={i % 2 === 0 ? "#22c55e33" : "#ef444433"}
                  />
                </g>
              ))}
            </g>
          </svg>
        </div>

        {/* Grid lines */}
        <div className="absolute inset-0 grid grid-cols-4 grid-rows-4">
          {Array.from({ length: 5 }).map((_, i) => (
            <div key={`h-${i}`} className="border-t border-white/5" style={{ top: `${(i / 4) * 100}%` }} />
          ))}
          {Array.from({ length: 5 }).map((_, i) => (
            <div key={`v-${i}`} className="border-l border-white/5" style={{ left: `${(i / 4) * 100}%` }} />
          ))}
        </div>
      </div>

      {/* Desktop Layout */}
      <div className="hidden md:block">
        <div className="relative w-full max-w-7xl mx-auto flex flex-col lg:flex-row items-center px-4 md:px-8 lg:px-12 py-8 md:py-16 lg:py-20">
          {/* Left Content */}
          <div
            className="w-full lg:w-1/2 space-y-6 text-center lg:text-left z-10"
            style={{
              transform: isMounted ? `translateY(${scrollY * -0.05}px)` : "none",
              transition: "transform 0.2s ease-out",
            }}
          >
            <div className="inline-block px-3 py-1.5 rounded-full bg-gradient-to-r from-[#F97316]/30 to-[#F97316]/10 backdrop-blur-sm border border-[#F97316]/30 text-[#F97316] text-sm mb-4 shadow-lg shadow-[#F97316]/5">
              <span className="relative">
                <span className="absolute -left-1.5 top-1/2 -translate-y-1/2 w-1 h-1 rounded-full bg-[#F97316] animate-pulse"></span>
                Professional Trading Platform
              </span>
            </div>

            <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold text-white leading-tight tracking-tight">
              Your Journey to
              <span className="bg-gradient-to-r from-[#F97316] to-[#FBBF24] bg-clip-text text-transparent block mt-1 mb-1">
                Funded Trading
              </span>
              Starts Here
            </h1>

            <p className="text-gray-300 text-base sm:text-lg md:text-xl max-w-lg mx-auto lg:mx-0 leading-relaxed">
              Trade with confidence using our advanced platform and get funded up to $400,000. Join the elite community
              of professional traders.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 pt-6 justify-center lg:justify-start">
              <Button
                asChild
                className="bg-gradient-to-r from-[#F97316] to-[#EA580C] hover:from-[#EA580C] hover:to-[#C2410C] text-white px-6 py-6 text-base sm:text-lg w-full sm:w-auto rounded-xl shadow-lg shadow-orange-500/20 border border-orange-500/20 transition-all duration-300 hover:shadow-orange-500/30 relative overflow-hidden group"
              >
                <Link href="/signup">
                  <span className="absolute inset-0 w-full h-full bg-gradient-to-r from-white/10 to-transparent opacity-0 group-hover:opacity-20 transition-opacity duration-300"></span>
                  Get Started
                  <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform duration-300" />
                </Link>
              </Button>
              <Button
                asChild
                variant="outline"
                className="bg-gradient-to-r from-[#F97316] to-[#EA580C] hover:from-[#EA580C] hover:to-[#C2410C] text-white px-6 py-6 text-base sm:text-lg w-full sm:w-auto rounded-xl shadow-lg shadow-orange-500/20 border border-orange-500/20 transition-all duration-300 hover:shadow-orange-500/30 relative overflow-hidden group"
              >
                <Link href="/signup">
                  <span className="absolute inset-0 w-full h-full bg-white/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></span>
                  Learn More
                </Link>
              </Button>
            </div>

            <div className="flex flex-wrap justify-center lg:justify-start gap-3 pt-6">
              <div className="flex items-center gap-2 bg-gradient-to-r from-white/10 to-white/5 backdrop-blur-sm px-3 py-2 rounded-full border border-white/10 shadow-sm">
                <div className="w-2 h-2 rounded-full bg-green-500 shadow-sm shadow-green-500/50"></div>
                <span className="text-sm text-gray-200">24/7 Trading</span>
              </div>
              <div className="flex items-center gap-2 bg-gradient-to-r from-white/10 to-white/5 backdrop-blur-sm px-3 py-2 rounded-full border border-white/10 shadow-sm">
                <div className="w-2 h-2 rounded-full bg-green-500 shadow-sm shadow-green-500/50"></div>
                <span className="text-sm text-gray-200">Instant Withdrawals</span>
              </div>
              <div className="flex items-center gap-2 bg-gradient-to-r from-white/10 to-white/5 backdrop-blur-sm px-3 py-2 rounded-full border border-white/10 shadow-sm">
                <div className="w-2 h-2 rounded-full bg-green-500 shadow-sm shadow-green-500/50"></div>
                <span className="text-sm text-gray-200">Advanced Analytics</span>
              </div>
            </div>
          </div>

          {/* Right Content - Trading Platform Image */}
          <div className="w-full lg:w-1/2 mt-8 lg:mt-0">
            <div className="relative w-full h-full">
              <Image
                src="/images/trading-platform.webp"
                alt="Trading Platform on Desktop and Mobile"
                width={600}
                height={400}
                className="w-full h-auto object-contain transform hover:scale-105 transition-transform duration-500 rounded-lg shadow-2xl"
                priority
              />

              {/* Glow effect behind the image */}
              <div className="absolute -inset-1 bg-gradient-to-r from-[#F97316]/20 to-blue-500/20 rounded-lg blur-xl opacity-70 -z-10"></div>
            </div>
          </div>
        </div>

        {/* Platform Logos - Desktop */}
        <div className="relative w-full max-w-7xl mx-auto px-4 md:px-8 lg:px-12 pb-16 z-10">
          <div className="bg-gradient-to-b from-white/8 to-white/4 backdrop-blur-md rounded-2xl p-6 border border-white/10 shadow-xl">
            <p className="text-gray-300 text-sm mb-6 text-center font-medium">Supported Trading Platforms</p>
            <div className="grid grid-cols-3 gap-4 sm:gap-8 justify-items-center">
              {[
                { src: "/images/mt4-logo.png", alt: "MetaTrader 4", name: "MetaTrader 4" },
                { src: "/images/mt5-logo.png", alt: "MetaTrader 5", name: "MetaTrader 5" },
                { src: "/images/ctrader-logo.png", alt: "cTrader", name: "cTrader" },
              ].map((platform, index) => (
                <div key={index} className="flex flex-col items-center group">
                  <div className="relative p-4 rounded-xl bg-gradient-to-b from-white/10 to-transparent backdrop-blur-sm border border-white/10 transition-all duration-300 group-hover:from-white/15 shadow-lg">
                    <div className="absolute inset-0 bg-gradient-to-b from-white/5 to-transparent rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    <Image
                      src={platform.src || "/placeholder.svg"}
                      alt={platform.alt}
                      width={80}
                      height={80}
                      className="h-12 sm:h-16 md:h-20 w-auto object-contain transition-transform duration-300 group-hover:scale-110"
                    />
                  </div>
                  <p className="text-gray-300 text-xs mt-3 group-hover:text-white transition-colors duration-300 font-medium">
                    {platform.name}
                  </p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Mobile Layout - Streamlined with LARGER IMAGES and filling the screen */}
      <div className="md:hidden flex flex-col min-h-screen justify-between">
        {/* Mobile Content Container */}
        <div className="flex flex-col h-full px-4 pt-24 pb-6">
          {/* Main Heading */}
          <div className="mb-10">
            <div className="inline-block px-3 py-1.5 rounded-full bg-gradient-to-r from-[#F97316]/30 to-[#F97316]/10 backdrop-blur-sm border border-[#F97316]/30 text-[#F97316] text-sm mb-6">
              <span className="relative">
                <span className="absolute -left-1.5 top-1/2 -translate-y-1/2 w-1 h-1 rounded-full bg-[#F97316] animate-pulse"></span>
                Professional Trading Platform
              </span>
            </div>

            <h1 className="text-3xl sm:text-4xl font-bold text-white leading-tight tracking-tight">
              Your Journey to
              <span className="bg-gradient-to-r from-[#F97316] to-[#FBBF24] bg-clip-text text-transparent block mt-1 mb-1">
                Funded Trading
              </span>
              Starts Here
            </h1>

            {/* One-line subheading */}
            <p className="text-gray-300 text-base mt-4">Get funded up to $400,000 with our professional platform</p>
          </div>

          {/* Primary CTA Button */}
          <Button className="w-full bg-gradient-to-r from-[#F97316] to-[#EA580C] text-white px-6 py-5 text-lg rounded-xl shadow-lg shadow-orange-500/20 border border-orange-500/20 mb-10">
            Get Started
            <ArrowRight className="ml-2 h-5 w-5" />
          </Button>

          {/* LARGER Trading Platform Image for Mobile */}
          <div className="relative w-full mb-10">
            <div className="relative w-full h-full">
              <Image
                src="/images/trading-platform.webp"
                alt="Trading Platform on Desktop and Mobile"
                width={600}
                height={400}
                className="w-full h-auto object-contain transform hover:scale-105 transition-transform duration-500 rounded-lg shadow-2xl"
                priority
              />

              {/* Glow effect behind the image */}
              <div className="absolute -inset-1 bg-gradient-to-r from-[#F97316]/20 to-blue-500/20 rounded-lg blur-xl opacity-70 -z-10"></div>
            </div>
          </div>

          {/* LARGER Platform Logos for Mobile */}
          <div className="w-full mt-auto">
            <p className="text-gray-300 text-sm mb-4 text-center font-medium">Supported Trading Platforms</p>
            <div className="grid grid-cols-3 gap-4 justify-items-center">
              {[
                { src: "/images/mt4-logo.png", alt: "MetaTrader 4", name: "MT4" },
                { src: "/images/mt5-logo.png", alt: "MetaTrader 5", name: "MT5" },
                { src: "/images/ctrader-logo.png", alt: "cTrader", name: "cTrader" },
              ].map((platform, index) => (
                <div key={index} className="flex flex-col items-center">
                  <div className="p-3 rounded-xl bg-gradient-to-b from-white/10 to-transparent backdrop-blur-sm border border-white/10 shadow-lg">
                    <Image
                      src={platform.src || "/placeholder.svg"}
                      alt={platform.alt}
                      width={100}
                      height={100}
                      className="h-14 w-auto object-contain"
                    />
                  </div>
                  <p className="text-gray-300 text-xs mt-2 font-medium">{platform.name}</p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default HeroSection
