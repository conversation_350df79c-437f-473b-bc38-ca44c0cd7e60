import { But<PERSON> } from "@/components/ui/button"
import { Facebook, Instagram, Send, ExternalLink } from 'lucide-react'
import { DiscordLogoIcon } from "@radix-ui/react-icons"
import Image from 'next/image'
import Link from 'next/link'
import { motion } from "framer-motion"
import { Inter } from "next/font/google"

// Define Inter font with multiple weights
const inter = Inter({
  subsets: ['latin'],
  display: 'swap',
})

export function CommunityCallToAction() {
  return (
    <div className="relative min-h-[500px] sm:min-h-[600px] w-full bg-[#0A0F1C] py-12 sm:py-20">
      {/* Simplified background */}
      <div className="absolute inset-0 bg-[url('/grid.svg')] opacity-10" />
      <div className="absolute inset-0 bg-gradient-to-br from-blue-900/20 via-transparent to-orange-900/10" />

      <div className="relative container mx-auto px-4">
        <div className="max-w-5xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="flex flex-col lg:flex-row items-center gap-8 sm:gap-12 bg-gradient-to-br from-gray-900/80 to-gray-800/60 p-6 sm:p-10 rounded-2xl border border-white/10 shadow-xl backdrop-blur-sm"
          >
            <div className="flex-1 text-left">
              <motion.h2
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.2, duration: 0.5 }}
                className={`${inter.className} text-3xl sm:text-4xl md:text-5xl font-extrabold mb-4 sm:mb-6 text-center lg:text-left tracking-tight leading-tight`}
              >
                <span className="bg-clip-text text-transparent bg-gradient-to-r from-orange-400 via-orange-300 to-blue-600 inline-block">
                  Join Funded Horizon
                </span>
                <br />
                <span className="text-white inline-block mt-1">Trading Network</span>
              </motion.h2>

              <motion.p
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.3, duration: 0.5 }}
                className={`${inter.className} text-base sm:text-lg mb-6 sm:mb-8 p-5 rounded-lg bg-gradient-to-br from-gray-800/40 to-gray-900/40 text-gray-100 leading-relaxed border border-white/5 shadow-inner`}
              >
                Connect with professional traders, access exclusive market insights, and elevate your trading journey with our vibrant community. Get real-time market analysis, trading strategies, and personalized mentorship from industry experts.
              </motion.p>

              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4, duration: 0.5 }}
                className="flex flex-col sm:flex-row gap-3 sm:gap-4"
              >
                <Link href="/signup">
                  <Button className="bg-gradient-to-r from-orange-500 to-blue-600 hover:from-orange-600 hover:to-blue-700 text-white px-8 py-6 rounded-lg font-bold transition-all duration-300 shadow-lg shadow-orange-500/20 hover:shadow-blue-600/30 hover:scale-105">
                    Join Our Network
                  </Button>
                </Link>
                <Button className="bg-white/5 hover:bg-white/15 text-white px-8 py-6 rounded-lg font-bold border border-white/10 hover:border-white/30 transition-all duration-300 backdrop-blur-sm hover:scale-105">
                  Learn More <ExternalLink className="ml-2 h-4 w-4" />
                </Button>
              </motion.div>
            </div>

            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.5, duration: 0.5 }}
              className="flex-1 w-full lg:w-auto"
            >
              <div className="grid grid-cols-2 gap-4 sm:gap-6">
                {[
                  {
                    Icon: Facebook,
                    label: "Facebook",
                    link: "https://www.facebook.com/fundedhorizon1/",
                    hoverBg: "hover:bg-blue-900/30",
                    hoverBorder: "hover:border-blue-500/50",
                    iconColor: "group-hover:text-blue-400",
                    ringColor: "group-hover:ring-blue-500/50"
                  },
                  {
                    Icon: Instagram,
                    label: "Instagram",
                    link: "https://www.instagram.com/fundedhorizon",
                    hoverBg: "hover:bg-purple-900/30",
                    hoverBorder: "hover:border-pink-500/50",
                    iconColor: "group-hover:text-pink-400",
                    ringColor: "group-hover:ring-pink-500/50"
                  },
                  {
                    Icon: DiscordLogoIcon,
                    label: "Discord",
                    link: "https://discord.gg/ZzG8demuuz",
                    hoverBg: "hover:bg-indigo-900/30",
                    hoverBorder: "hover:border-indigo-500/50",
                    iconColor: "group-hover:text-indigo-400",
                    ringColor: "group-hover:ring-indigo-500/50"
                  },
                  {
                    Icon: Send,
                    label: "Telegram",
                    link: "https://t.me/fundedhorizon",
                    hoverBg: "hover:bg-sky-900/30",
                    hoverBorder: "hover:border-sky-500/50",
                    iconColor: "group-hover:text-sky-400",
                    ringColor: "group-hover:ring-sky-500/50"
                  }
                ].map(({ Icon, label, link, hoverBg, hoverBorder, iconColor, ringColor }, index) => (
                  <motion.a
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.6 + (index * 0.1), duration: 0.4 }}
                    key={label}
                    href={link}
                    target="_blank"
                    rel="noopener noreferrer"
                    className={`group bg-white/5 p-6 rounded-2xl flex flex-col items-center justify-center gap-3 ${hoverBg} hover:scale-105 transition-all duration-300 border border-white/5 ${hoverBorder} backdrop-blur-sm`}
                  >
                    <div className={`bg-white/10 rounded-full p-3 ring-1 ring-white/5 ${ringColor} transition-all duration-300 transform group-hover:scale-110`}>
                      <Icon className={`w-7 h-7 text-white ${iconColor} transition-colors duration-300`} />
                    </div>
                    <span className={`${inter.className} text-white font-medium text-base group-hover:font-semibold transition-all duration-300`}>{label}</span>
                    <div className="w-2 h-2 bg-green-500 rounded-full group-hover:scale-125 transition-transform duration-300" />
                  </motion.a>
                ))}
              </div>
            </motion.div>
          </motion.div>
        </div>
      </div>
    </div>
  )
}
